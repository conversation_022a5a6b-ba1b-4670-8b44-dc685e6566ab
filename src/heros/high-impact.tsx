"use client";

import { useRef, type FC } from "react";
import gsap from "gsap";
import type { Page } from "@/payload-types";
import { ParallaxImage } from "@/components/motion/parallax-image";
import { useGSAP } from "@gsap/react";
import { Split } from "@/components/motion/split";
import { useEntryAnimation } from "@/lib/transitions";

export const HighImpact: FC<Page["hero"]> = ({ content, title, image }) => {
	const heroRef = useRef<HTMLDivElement>(null);

	useGSAP(
		() => {
			if (!heroRef.current) return;

			const hero = heroRef.current;

			const naturalHeight = hero.offsetHeight;
			const viewportHeight = window.innerHeight;

			const initialScaleY = viewportHeight / naturalHeight;
			gsap.set(hero, {
				transformOrigin: "top",
				scaleY: initialScaleY,
			});
		},
		{ scope: heroRef },
	);

	useEntryAnimation(
		() => {
			if (!heroRef.current) return;

			const hero = heroRef.current;

			gsap.to(hero, {
				scaleY: 1,
				duration: 1.5,
				ease: "expo.out",
				delay: 0.5,
			});

			return () => {
				gsap.set(hero, { clearProps: "transform" });
			};
		},
		{ scope: heroRef },
	);

	return (
		<>
			<section
				className="p-5 will-change-transform origin-top overflow-hidden relative z-10 bg-primary !mb-0"
				ref={heroRef}
			>
				<div className="default-grid | pt-[30vw]">
					<div className="col-span-full lg:col-span-8">
						<Split delay={1} animationOnScroll={false}>
							<h1 className="sm:indent-[25%] h3">{title}</h1>
						</Split>
					</div>
					<div className="col-span-full lg:col-span-4 flex flex-col items-end justify-between">
						<Split delay={1.25} animationOnScroll={false}>
							<p className="max-lg:mt-8 max-lg:max-w-[32rem] text-right text-balance">
								{content}
							</p>
						</Split>
					</div>
				</div>
			</section>
			<section className="relative h-[800px] md:h-[100svh] !mb-[20vw]">
				<div className="absolute inset-0 overflow-hidden">
					<ParallaxImage media={image} />
				</div>
				<div className="absolute inset-x-0 bottom-0 translate-y-2/3 pointer-events-none">
					<img src="/swirl.svg" alt="swirl" className="w-full h-auto" />
				</div>
			</section>
		</>
	);
};
