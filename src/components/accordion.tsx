"use client";

import * as AccordionPrimitive from "@radix-ui/react-accordion";
import { cn } from "@/lib/utils";
import { ChevronUp } from "lucide-react";
import type { ReactNode } from "react";

// Accordion root
export function Accordion({
	children,
	...props
}: AccordionPrimitive.AccordionMultipleProps) {
	return (
		<AccordionPrimitive.Root className="divide-y divide-black" {...props}>
			{children}
		</AccordionPrimitive.Root>
	);
}

// Accordion item
export function AccordionItem({
	value,
	children,
}: {
	value: string;
	children: ReactNode;
}) {
	return (
		<AccordionPrimitive.Item value={value} className="py-2">
			{children}
		</AccordionPrimitive.Item>
	);
}

// Accordion trigger with your layout and icon
export function AccordionTrigger({
	children,
	className,
}: {
	children: ReactNode;
	className?: string;
}) {
	return (
		<AccordionPrimitive.Header className="p">
			<AccordionPrimitive.Trigger
				className={cn(
					"w-full default-grid pb-4",
					"focus:outline-none group",
					className,
				)}
			>
				<div className="col-span-5 flex items-center">
					<div className="flex items-center gap-x-2">
						<ChevronUp
							className={cn(
								"size-6 text-muted-foreground transition-transform duration-300",
								"group-data-[state=open]:rotate-180",
							)}
						/>
						<span className="text-sm font-medium group-data-[state=open]:text-red-600">
							{/* Icon text changes */}
							<AccordionPrimitive.Trigger asChild>
								<span className="pointer-events-none">
									<span className="sr-only">Toggle</span>
									<span className="hidden group-data-[state=open]:inline">
										schließen
									</span>
									<span className="inline group-data-[state=open]:hidden">
										öffnen
									</span>
								</span>
							</AccordionPrimitive.Trigger>
						</span>
					</div>
				</div>
				<div className="col-start-6 col-span-7">{children}</div>
			</AccordionPrimitive.Trigger>
		</AccordionPrimitive.Header>
	);
}

// Accordion content with smooth height transition
export function AccordionContent({
	children,
	className,
}: {
	children: ReactNode;
	className?: string;
}) {
	return (
		<AccordionPrimitive.Content
			className={cn(
				"data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down duration-500 ease-in-out-expo overflow-hidden",
				className,
			)}
		>
			{children}
		</AccordionPrimitive.Content>
	);
}
