"use client";

import { cn } from "@/lib/utils";
import type { Concert } from "@/payload-types";
import type { FC } from "react";
import { Magnet } from "./motion/magnet";
import { Media } from "./render/render-media";

interface IConcertItemProps {
	concert: Concert;
	isMostRecent?: boolean;
	className?: string;
}

export const ConcertItem: FC<IConcertItemProps> = ({
	className,
	concert,
	isMostRecent,
}) => {
	return (
		<div className={cn("", className)}>
			<div className="relative">
				<Media
					resource={concert.image}
					className="aspect-video mb-8"
					imgClassName="object-cover h-full w-full"
				/>
				{isMostRecent && (
					<div className="absolute left-0 top-3/4 -translate-x-1/4 -translate-y-1/2">
						<Magnet padding={5000}>
							<div className=" size-40 bg-contrast rounded-full grid place-items-center text-sm">
								Gib mir mehr infos
							</div>
						</Magnet>
					</div>
				)}
			</div>
			<div className="mb-8">
				<h3>
					<strong>{concert.title}</strong>
				</h3>
				<p className="h3">{concert.subline}</p>
			</div>
			<div className="grid [grid-template-columns:max-content_1fr] gap-x-4 gap-y-2">
				<p>
					<strong>WANN:</strong>
				</p>
				<p>{concert.formattedDateString}</p>
				<p>
					<strong>WO:</strong>
				</p>
				<p>{concert.where}</p>
			</div>
		</div>
	);
};
