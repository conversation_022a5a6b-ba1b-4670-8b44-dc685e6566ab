"use client";

import cn from "clsx";
import type { LenisOptions } from "lenis";
import { <PERSON><PERSON> } from "./lenis";

interface WrapperProps extends React.HTMLAttributes<HTMLDivElement> {
	lenis?: boolean | LenisOptions;
}

export function Wrapper({
	children,
	className,
	lenis = true,
	...props
}: WrapperProps) {
	return (
		<>
			<main
				className={cn("relative flex grow flex-col !space-y-32", className)}
				{...props}
			>
				<div className="revealer | min-h-[100svh] pointer-events-none fixed inset-0 z-10 flex origin-top items-center justify-center bg-contrast" />
				{children}
			</main>
			{lenis && <Lenis root options={typeof lenis === "object" ? lenis : {}} />}
		</>
	);
}
