"use client";

import { useRef, type FC } from "react";
import Link from "next/link";
import type { Head<PERSON>, Page } from "@/payload-types";
import { CMSLink } from "../cms-link";
import { MegaNav } from "./mega-nav";

interface INavigationProps {
	header: Header;
}

export const Navigation: FC<INavigationProps> = ({ header }) => {
	const navRef = useRef<HTMLDivElement>(null);

	return (
		<header className="fixed inset-x-0 top-0 z-50 layout-block">
			<div className="default-grid py-safe" ref={navRef}>
				<CMSLink
					type="reference"
					reference={{ relationTo: "pages", value: { slug: "home" } as Page }}
					className="relative col-span-4 cursor-pointer"
				>
					<span className="inline-block bg-contrast px-1 text-sm">
						HARD CHOR
					</span>
				</CMSLink>
				<nav className="col-span-8 col-start-5 flex justify-between">
					<ul className="flex items-center gap-x-[calc(var(--gap)/2)]">
						{header.navItems?.map((item) => {
							if (item.isMegaNav) {
								return (
									<li key={item.id} className="text-sm relative">
										<MegaNav item={item} navRef={navRef}>
											{item.label}
										</MegaNav>
									</li>
								);
							}

							return (
								<li key={item.id} className="text-sm">
									<CMSLink {...item.link} />
								</li>
							);
						})}
					</ul>
					<Link href={"/contact"} className="text-sm">
						Get Started
					</Link>
				</nav>
			</div>
		</header>
	);
};
