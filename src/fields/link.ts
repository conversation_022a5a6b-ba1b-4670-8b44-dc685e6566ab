import deepMerge from "@/utils/deepMerge";
import type { Field, GroupField } from "payload";

export type LinkAppearances = "default";

export const appearanceOptions: Record<
	LinkAppearances,
	{ label: string; value: string }
> = {
	default: {
		label: "Default",
		value: "default",
	},
};

type LinkType = (options?: {
	appearances?: LinkAppearances[] | false;
	disableLabel?: boolean;
	overrides?: Partial<GroupField>;
}) => Field;

export const link: LinkType = ({
	appearances,
	disableLabel = false,
	overrides = {},
} = {}) => {
	const linkResult: GroupField = {
		name: "link",
		label: "Verlinkung",
		type: "group",
		admin: {
			hideGutter: true,
		},
		fields: [
			{
				type: "row",
				fields: [
					{
						name: "type",
						type: "radio",
						admin: {
							layout: "horizontal",
							width: "50%",
						},
						defaultValue: "reference",
						options: [
							{
								label: "Interner Link",
								value: "reference",
							},
							{
								label: "Externe URL",
								value: "custom",
							},
						],
					},
					{
						name: "newTab",
						type: "checkbox",
						admin: {
							style: {
								alignSelf: "flex-end",
							},
							width: "50%",
						},
						label: "In neuem Tab <PERSON>",
					},
				],
			},
		],
	};

	const linkTypes: Field[] = [
		{
			name: "reference",
			type: "relationship",
			admin: {
				condition: (_, siblingData) => siblingData?.type === "reference",
			},
			label: "Verlinkte Seite",
			relationTo: ["pages"],
			required: true,
		},
		{
			name: "url",
			type: "text",
			admin: {
				condition: (_, siblingData) => siblingData?.type === "custom",
			},
			label: "Externe URL",
			required: true,
		},
	];

	if (!disableLabel) {
		linkTypes.map((linkType) => ({
			...linkType,
			admin: {
				...linkType.admin,
				width: "50%",
			},
		}));

		linkResult.fields.push({
			type: "row",
			fields: [
				...linkTypes,
				{
					name: "label",
					type: "text",
					admin: {
						width: "50%",
					},
					label: "Label",
					required: true,
				},
			],
		});
	} else {
		linkResult.fields = [...linkResult.fields, ...linkTypes];
	}

	if (appearances !== false) {
		let appearanceOptionsToUse = [appearanceOptions.default];

		if (appearances) {
			appearanceOptionsToUse = appearances.map(
				(appearance) => appearanceOptions[appearance],
			);
		}

		linkResult.fields.push({
			name: "appearance",
			type: "select",
			admin: {
				description: "Choose how the link should be rendered.",
			},
			defaultValue: "default",
			options: appearanceOptionsToUse,
		});
	}

	return deepMerge(linkResult, overrides);
};
