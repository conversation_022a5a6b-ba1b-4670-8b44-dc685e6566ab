import type { <PERSON>ada<PERSON> } from "next";
import { projectInfo } from "./get-project-info";

const defaultOpenGraph: Metadata["openGraph"] = {
	type: "website",
	description: projectInfo.description,
	images: [
		{
			url: process.env.NEXT_PUBLIC_SERVER_URL
				? `${process.env.NEXT_PUBLIC_SERVER_URL}/website-template-OG.webp`
				: "/website-template-OG.webp",
		},
	],
	siteName: projectInfo.name,
	title: projectInfo.name,
};

export const mergeOpenGraph = (og?: Metadata["openGraph"]): Metadata["openGraph"] => {
	return {
		...defaultOpenGraph,
		...og,
		images: og?.images ? og.images : defaultOpenGraph.images,
	};
};
