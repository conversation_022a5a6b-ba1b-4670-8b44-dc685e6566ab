import type { Block } from "payload";

export const ConcertArchiveBlockConfig: Block = {
	slug: "concert-archive",
	imageURL: "/blocks/concert-archive-block.png",
	labels: {
		singular: "Konzert Archiv Sektion",
		plural: "Konzert Archiv Sektionen",
	},
	fields: [
		{
			name: "populateBy",
			type: "select",
			label: "Befüllung basierend auf",
			defaultValue: "all",
			options: [
				{
					label: "Alle Konzerte",
					value: "all",
				},
				{
					label: "Nächstes Konzert",
					value: "next",
				},
				{
					label: "Kommende Konzerte",
					value: "upcoming",
				},
				{
					label: "Individuelle Auswahl",
					value: "selection",
				},
			],
		},
		{
			name: "limit",
			type: "number",
			admin: {
				condition: (_, siblingData) =>
					Boolean(["all", "upcoming"].includes(siblingData?.populateBy)),
				step: 1,
			},
			defaultValue: 3,
			label: "Limit",
		},
		{
			name: "selectedDocs",
			type: "relationship",
			admin: {
				condition: (_, siblingData) => siblingData.populateBy === "selection",
			},
			hasMany: true,
			label: "Auswahl",
			relationTo: ["concerts"],
		},
	],
	interfaceName: "ConcertArchiveBlock",
};
