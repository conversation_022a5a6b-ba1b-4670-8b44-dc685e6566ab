import { ConcertItem } from "@/components/concert-item";
import type {
	Concert,
	ConcertArchiveBlock as IConcertArchiveBlockProps,
} from "@/payload-types";
import configPromise from "@payload-config";
import { getPayload } from "payload";
import type { FC } from "react";

const pattern = [
	"col-start-5 col-span-8",
	"col-start-1 col-span-4",
	"col-start-5 col-span-4",
	"col-start-1 col-span-8",
	"col-start-9 col-span-4",
];

export const ConcertArchiveBlock: FC<IConcertArchiveBlockProps> = async ({
	limit: limitProp = 1,
	populateBy,
	selectedDocs,
}) => {
	const payload = await getPayload({ config: configPromise });

	if (populateBy === "next") {
		const concert = await payload
			.find({
				collection: "concerts",
				depth: 1,
				where: {
					"dates.date": {
						greater_than_equal: new Date().toISOString(),
					},
				},
				sort: "dates.date",
				limit: 1,
			})
			.then((res) => res.docs[0]);

		if (!concert) {
			console.warn("No upcoming concert found");
			return null;
		}

		return (
			<section className="layout-block">
				<div className="default-grid gap-y-24">
					<ConcertItem
						isMostRecent
						concert={concert}
						className="col-start-3 col-span-8"
					/>
				</div>
			</section>
		);
	}

	if (populateBy === "selection") {
		if (!selectedDocs?.length) {
			console.warn("No concerts selected");
			return null;
		}

		const concerts = selectedDocs.map((post) => {
			if (typeof post.value === "object") return post.value;
		}) as Concert[];

		return (
			<section className="layout-block">
				<div className="default-grid gap-y-24">
					{concerts.map((concert, index) => (
						<ConcertItem
							key={concert.id}
							isMostRecent={index === 0}
							concert={concert}
							className={pattern[index % pattern.length]}
						/>
					))}
				</div>
			</section>
		);
	}

	const concerts = await payload
		.find({
			collection: "concerts",
			depth: 1,
			sort: "dates.date",
			where:
				populateBy === "upcoming"
					? {
							"dates.date": {
								greater_than_equal: new Date().toISOString(),
							},
						}
					: undefined,
			limit: limitProp || 3,
		})
		.then((res) => res.docs);

	return (
		<section className="layout-block">
			<div className="default-grid gap-y-24">
				{concerts.map((concert, index) => (
					<ConcertItem
						key={concert.id}
						isMostRecent={index === 0}
						concert={concert}
						className={pattern[index % pattern.length]}
					/>
				))}
			</div>
		</section>
	);
};
