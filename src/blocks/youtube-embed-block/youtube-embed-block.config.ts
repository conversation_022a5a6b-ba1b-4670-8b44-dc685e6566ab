import type { Block } from "payload";

export const YoutubeEmbedBlockConfig: Block = {
	slug: "youtube-embed",
	imageURL: "/blocks/youtube-embed-block.png",
	labels: {
		singular: "Youtube Embed Sektion",
		plural: "Youtube Embed Sektionen",
	},
	fields: [
		{
			name: "url",
			type: "text",
			required: true,
		},
		{
			name: "yt_id",
			type: "text",
			admin: { hidden: true },
			hooks: {
				beforeValidate: [
					({ siblingData }) => new URL(siblingData.url).searchParams.get("v"),
				],
			},
		},
	],
	interfaceName: "YoutubeEmbedBlock",
};
