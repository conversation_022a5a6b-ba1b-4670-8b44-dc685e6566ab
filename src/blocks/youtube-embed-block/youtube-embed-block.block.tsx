"use client";

import "react-lite-youtube-embed/dist/LiteYouTubeEmbed.css";

import type { FC } from "react";
import LiteYouTubeEmbed from "react-lite-youtube-embed";
import type { YoutubeEmbedBlock as IYoutubeEmbedBlockProps } from "@/payload-types";

export const YoutubeEmbedBlock: FC<IYoutubeEmbedBlockProps> = ({
	url,
	yt_id,
}) => {
	return (
		<section className="layout-block">
			<LiteYouTubeEmbed id={yt_id as string} title={url as string} />
		</section>
	);
};
