import {
	BlocksFeature,
	lexicalEditor,
	LinkFeature,
} from "@payloadcms/richtext-lexical";
import type { Block } from "payload";

export const InlineButton: Block = {
	slug: "inline-button",
	interfaceName: "InlineButton",
	fields: [
		{
			type: "row",
			fields: [
				{
					name: "type",
					type: "radio",
					admin: {
						layout: "horizontal",
						width: "50%",
					},
					defaultValue: "reference",
					options: [
						{
							label: "Interner Link",
							value: "reference",
						},
						{
							label: "Externe URL",
							value: "custom",
						},
					],
				},
				{
					name: "newTab",
					type: "checkbox",
					admin: {
						style: {
							alignSelf: "flex-end",
						},
						width: "50%",
					},
					label: "In neuem Tab öffnen",
				},
			],
		},
		{
			type: "row",
			fields: [
				{
					name: "label",
					type: "text",
					admin: {
						width: "50%",
					},
					label: "Label",
					required: true,
				},
				{
					name: "reference",
					type: "relationship",
					admin: {
						condition: (_, siblingData) => siblingData?.type === "reference",
						width: "50%",
					},
					label: "Verlinkte Seite",
					relationTo: ["pages"],
					required: true,
				},
				{
					name: "url",
					type: "text",
					admin: {
						condition: (_, siblingData) => siblingData?.type === "custom",
						width: "50%",
					},
					label: "Externe URL",
					required: true,
				},
			],
		},
	],
};

export const ConversionBlockConfig: Block = {
	slug: "conversion",
	imageURL: "/blocks/conversion-block.png",
	labels: {
		singular: "Conversion Sektion",
		plural: "Conversion Sektionen",
	},
	fields: [
		{
			name: "content",
			type: "richText",
			label: false,
			editor: lexicalEditor({
				features: ({ rootFeatures }) => {
					return [
						...rootFeatures,
						BlocksFeature({
							inlineBlocks: [InlineButton],
						}),
					];
				},
			}),
		},
		{
			name: "variant",
			type: "select",
			label: "Variante",
			options: [
				{
					label: "Hintergrund Bild",
					value: "image",
				},
				{
					label: "Punk Kopf",
					value: "punk",
				},
			],
			defaultValue: "image",
		},
		{
			name: "subtext",
			label: "Subtext",
			type: "text",
			admin: {
				condition: (_, siblingData) => siblingData.variant === "image",
			},
		},
		{
			name: "image",
			type: "upload",
			label: "Bild",
			relationTo: "media",
			admin: {
				condition: (_, siblingData) => siblingData.variant === "image",
			},
		},
	],
	interfaceName: "ConversionBlock",
};
