import type { Block } from "payload";

export const AssetArchiveBlockConfig: Block = {
	slug: "asset-archive",
	imageURL: "/blocks/asset-archive-block.png",
	labels: {
		singular: "Medien Archiv Sektion",
		plural: "Medien Archiv Sektionen",
	},
	fields: [
		{
			name: "items",
			type: "array",
			label: "Sektionen",
			fields: [
				{
					name: "title",
					label: "Titel",
					type: "text",
					required: true,
				},
				{
					name: "type",
					type: "select",
					label: "Typ",
					options: [
						{ label: "Bilder", value: "images" },
						{ label: "Audio", value: "audios" },
						{ label: "Video", value: "videos" },
					],
					required: true,
				},
				{
					name: "assets_images",
					label: "Medien",
					type: "array",
					admin: {
						condition: (_, siblingData) => siblingData.type === "images",
					},
					fields: [
						{
							name: "image",
							type: "upload",
							relationTo: "media",
							required: true,
						},
						{
							name: "title",
							label: "Titel",
							type: "text",
							required: true,
						},
						{
							name: "metadata",
							type: "text",
							label: "Information",
						},
						{
							name: "copyright",
							type: "text",
							label: "Copyright",
						},
					],
				},
				{
					name: "assets_videos",
					label: "Medien",
					type: "array",
					admin: {
						condition: (_, siblingData) => siblingData.type === "videos",
					},
					fields: [
						{
							name: "source",
							type: "select",
							label: "Quelle",
							options: [
								{ label: "Youtube", value: "youtube" },
								{ label: "Eigener Upload", value: "upload" },
							],
							required: true,
						},
						{
							name: "yt_url",
							type: "text",
							label: "Youtube URL",
							admin: {
								condition: (_, siblingData) => siblingData.source === "youtube",
							},
							required: true,
						},
						{
							name: "yt_id",
							type: "text",
							admin: {
								hidden: true,
								condition: (_, siblingData) => siblingData.source === "youtube",
							},
							hooks: {
								beforeValidate: [
									({ siblingData }) =>
										new URL(siblingData.yt_url).searchParams.get("v"),
								],
							},
						},
						{
							name: "video",
							type: "upload",
							relationTo: "media",
							admin: {
								condition: (_, siblingData) => siblingData.source === "upload",
							},
							required: true,
						},
					],
				},
				{
					name: "assets_audios",
					label: "Medien",
					type: "array",
					admin: {
						condition: (_, siblingData) => siblingData.type === "audios",
					},
					fields: [
						{
							name: "audio",
							type: "upload",
							relationTo: "media",
							required: true,
						},
						{
							name: "title",
							label: "Titel",
							type: "text",
							required: true,
						},
					],
				},
			],
		},
	],
	interfaceName: "AssetArchiveBlock",
};
