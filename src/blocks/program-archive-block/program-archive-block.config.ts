import type { Block } from "payload";

export const ProgramArchiveBlockConfig: Block = {
	slug: "program-archive",
	imageURL: "/blocks/program-archive-block.png",
	labels: {
		singular: "Programm Archiv Sektion",
		plural: "Programm Archiv Sektionen",
	},
	fields: [
		{
			name: "populateBy",
			type: "select",
			label: "Befüllung basierend auf",
			defaultValue: "all",
			options: [
				{
					label: "Alle",
					value: "all",
				},
				{
					label: "Individuelle Auswahl",
					value: "selection",
				},
			],
		},
		{
			name: "limit",
			type: "number",
			admin: {
				condition: (_, siblingData) => siblingData.populateBy === "collection",
				step: 1,
			},
			defaultValue: 10,
			label: "Limit",
		},
		{
			name: "selectedDocs",
			type: "relationship",
			admin: {
				condition: (_, siblingData) => siblingData.populateBy === "selection",
			},
			hasMany: true,
			label: "Auswahl",
			relationTo: ["programs"],
		},
		{
			name: "sort",
			type: "select",
			label: "Sortierung",
			defaultValue: "newest",
			options: [
				{
					label: "Neueste zuerst",
					value: "DESC",
				},
				{
					label: "Älteste zuerst",
					value: "ASC",
				},
			],
		},
	],
	interfaceName: "ProgramArchiveBlock",
};
