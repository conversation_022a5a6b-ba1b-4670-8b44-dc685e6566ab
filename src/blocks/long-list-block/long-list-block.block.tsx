import type { FC } from "react";
import type { LongListBlock as LongListBlockProps } from "@/payload-types";

import { cn } from "@/lib/utils";
import RichText from "@/components/render/render-rich-text";
import { ParallaxImage } from "@/components/motion/parallax-image";

export const LongListBlock: FC<LongListBlockProps> = ({
	columns,
	image,
	title,
	richText,
	id,
}) => {
	return (
		<section className="relative h-[100svh]">
			<div className="absolute inset-0 overflow-hidden">
				<ParallaxImage media={image} />
			</div>

			<div className="layout-block relative py-4 flex flex-col justify-between h-full">
				<div className="text-primary default-grid text-xs">
					{columns &&
						columns?.length > 0 &&
						columns.map((col, index) => {
							const { richText, width = "2" } = col;

							const colSpan = `col-span-${width}`;

							return (
								<div
									className={cn("col-span-12", `lg:${colSpan}`)}
									key={`${id}-${index * 1}`}
								>
									{richText && (
										<RichText data={richText} enableGutter={false} />
									)}
								</div>
							);
						})}
				</div>
				<div className="absolute top-1/2 -translate-y-1/2 w-full default-grid">
					<div className="col-start-6 col-span-7">
						<p className="h2 | text-contrast">{title}</p>
					</div>
				</div>

				<div className="text-primary default-grid">
					<div className="col-start-6 col-span-7">
						{richText && <RichText data={richText} enableGutter={false} />}
					</div>
				</div>
			</div>
		</section>
	);
};
