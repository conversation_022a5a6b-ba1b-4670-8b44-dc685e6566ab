import { FontColorFeature } from "@/lexical-features/font-color/font-color-feature.server";
import {
	lexicalEditor,
	FixedToolbarFeature,
	InlineToolbarFeature,
} from "@payloadcms/richtext-lexical";
import type { Block, Field } from "payload";

const columnFields: Field[] = [
	{
		name: "richText",
		type: "richText",
		editor: lexicalEditor({
			features: ({ rootFeatures }) => rootFeatures,
		}),
		label: false,
	},
	{
		name: "width",
		label: "Breite",
		type: "select",
		defaultValue: "2",
		options: [
			{ label: "1 Spaltern", value: "1" },
			{ label: "2 Spaltern", value: "2" },
			{ label: "3 Spaltern", value: "3" },
			{ label: "4 Spaltern", value: "4" },
			{ label: "5 Spaltern", value: "5" },
			{ label: "6 Spaltern", value: "6" },
			{ label: "7 Spaltern", value: "7" },
			{ label: "8 Spaltern", value: "8" },
			{ label: "9 Spaltern", value: "9" },
			{ label: "10 Spaltern", value: "10" },
			{ label: "11 Spaltern", value: "11" },
			{ label: "12 Spaltern", value: "12" },
		],
	},
];

export const LongListBlockConfig: Block = {
	slug: "long-list",
	imageURL: "/blocks/long-list-block.png",
	labels: {
		singular: "Vergangene Konzerte Teaser Sektion",
		plural: "Vergangene Konzerte Teaser Sektionen",
	},
	fields: [
		{
			name: "title",
			type: "text",
			required: true,
		},
		{
			name: "richText",
			type: "richText",
			editor: lexicalEditor({
				features: ({ rootFeatures }) => {
					return [
						...rootFeatures,
						FixedToolbarFeature(),
						InlineToolbarFeature(),
						FontColorFeature(),
					];
				},
			}),
		},
		{
			name: "image",
			type: "upload",
			relationTo: "media",
			required: true,
		},
		{
			name: "columns",
			type: "array",
			fields: columnFields,
		},
	],
	interfaceName: "LongListBlock",
};
