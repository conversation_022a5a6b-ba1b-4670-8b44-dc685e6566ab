import { link } from "@/fields/link";
import { FontColorFeature } from "@/lexical-features/font-color/font-color-feature.server";
import {
	FixedToolbarFeature,
	InlineToolbarFeature,
	lexicalEditor,
} from "@payloadcms/richtext-lexical";
import type { Block } from "payload";

export const ChoirDirectorBlockConfig: Block = {
	slug: "choir-director",
	imageURL: "/blocks/choir-director-block.png",
	labels: {
		singular: "Chorleiter Sektion",
		plural: "Chorleiter Sektionen",
	},
	fields: [
		{
			name: "richText",
			type: "richText",
			editor: lexicalEditor({
				features: ({ rootFeatures }) => {
					return [
						...rootFeatures,
						FixedToolbarFeature(),
						InlineToolbarFeature(),
						FontColorFeature(),
					];
				},
			}),
			label: false,
		},
		{
			name: "images",
			label: "Bilder",
			type: "upload",
			hasMany: true,
			relationTo: "media",
		},
		link({ appearances: false }),
		{
			name: "overlapWithNext",
			type: "checkbox",
			label: "Überlappen mit dem nächsten Block",
			admin: {
				description: "Macht nur Sinn wenn dieser Block der letzte ist.",
			},
		},
	],
	interfaceName: "ChoirDirectorBlock",
};
