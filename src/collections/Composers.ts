import type { CollectionConfig } from "payload";
import { revalidateArchive } from "./hooks/revalidate-archive";

export const Composers: CollectionConfig = {
	slug: "composers",
	labels: {
		singular: "Komponist",
		plural: "Komponisten",
	},
	admin: {
		useAsTitle: "name",
		defaultColumns: ["name", "title", "updatedAt"],
	},
	fields: [
		{
			name: "name",
			label: "Name",
			type: "text",
			required: true,
		},
		{
			name: "title",
			label: "Titel",
			type: "text",
		},
		{
			name: "letter",
			label: "Anfangsbuchstabe",
			type: "text",
			admin: {
				hidden: true,
			},
		},
	],
	hooks: {
		afterChange: [revalidateArchive],
		beforeChange: [
			({ data }) => {
				if (data?.name) {
					return {
						...data,
						letter: data.name[0].toUpperCase(),
					};
				}
				return data;
			},
		],
	},
};
