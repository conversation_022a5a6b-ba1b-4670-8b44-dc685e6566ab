import { lexicalEditor } from "@payloadcms/richtext-lexical";
import type { CollectionConfig } from "payload";
import { revalidateArchive } from "./hooks/revalidate-archive";

export const Programs: CollectionConfig = {
	slug: "programs",
	labels: {
		singular: "Programm",
		plural: "Programme",
	},
	admin: {
		useAsTitle: "title",
		defaultColumns: ["title", "segments", "updatedAt"],
	},
	fields: [
		{
			name: "title",
			label: "Titel",
			type: "text",
		},
		{
			name: "description",
			label: "Beschreibung",
			type: "richText",
			editor: lexicalEditor({
				features: ({ rootFeatures }) => rootFeatures,
			}),
		},
		{
			name: "segments",
			label: "Abschnitte",
			type: "array",
			fields: [
				{
					name: "title",
					label: "Titel",
					type: "text",
				},
			],
		},
	],
	hooks: {
		afterChange: [revalidateArchive],
	},
};
