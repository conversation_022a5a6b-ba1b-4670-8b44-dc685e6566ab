import type { CollectionConfig } from "payload";
import sharp from "sharp";
import path from "node:path";
import { access } from "@/access";

export const Media: CollectionConfig = {
	slug: "media",
	labels: {
		singular: "Medium",
		plural: "Medien",
	},
	access: {
		create: access.authenticated,
		delete: access.authenticated,
		read: access.anyone,
		update: access.authenticated,
	},
	fields: [
		{
			name: "alt",
			type: "text",
			admin: {
				condition: (_, siblingData) => {
					const mime = siblingData?.mimeType;
					return typeof mime === "string" && mime.startsWith("image/");
				},
			},
		},
		{
			name: "hasTransparency",
			type: "checkbox",
			admin: { hidden: true },
		},
		{
			name: "autoCompress",
			label: "Automatisch komprimieren",
			defaultValue: true,
			type: "checkbox",
		},
	],
	upload: {
		mimeTypes: ["image/*", "video/*", "audio/*"],
	},
	hooks: {
		beforeChange: [
			async ({ req, data }) => {
				const file = req.file;
				const mimeType = file?.mimetype;

				if (mimeType?.startsWith("image/")) {
					if (!data.autoCompress) return;
					if (!file?.data || !file.mimetype) return;

					try {
						const sharpInstance = sharp(file.data).resize({
							width: 2000,
							withoutEnlargement: true,
						});

						const metadata = await sharpInstance.metadata();
						const hasTransparency = metadata.hasAlpha === true;

						if (hasTransparency) {
							file.data = await sharpInstance.webp({ quality: 75 }).toBuffer();
							file.mimetype = "image/webp";
							file.name = file.name.replace(path.extname(file.name), ".webp");
						} else {
							file.data = await sharpInstance.jpeg({ quality: 75 }).toBuffer();
							file.mimetype = "image/jpeg";
							file.name = file.name.replace(path.extname(file.name), ".jpg");
						}

						file.size = file.data.length;

						const finalMeta = await sharp(file.data).metadata();

						return {
							...data,
							filesize: file.size,
							width: finalMeta.width,
							height: finalMeta.height,
							hasTransparency,
						};
					} catch (err) {
						console.warn("Image optimization/conversion failed:", err);
					}
				} else if (mimeType?.startsWith("video/")) {
					return data;
				}
			},
			async ({ data }) => {
				if (!data.alt && data.filename && data.mimeType?.startsWith("image/")) {
					return {
						...data,
						alt: data.filename.replace(path.extname(data.filename), ""),
					};
				}
				return data;
			},
		],
	},
};
