import { getPayload } from "payload";
import configPromise from "@payload-config";
import { NextResponse } from "next/server";
import { programs } from "./programs";

export const GET = async () => {
	try {
		const payloadInstance = await getPayload({ config: configPromise });
		const reversed = [...programs].reverse();

		for (const program of reversed) {
			await payloadInstance.create({
				collection: "programs",
				data: {
					title: program.title,
					segments: program.segments,
				},
			});
		}

		return NextResponse.json({ success: true });
	} catch (error) {
		console.error(error);
		return NextResponse.json(
			{ error: "INTERNAL_SERVER_ERROR" },
			{ status: 500 },
		);
	}
};
