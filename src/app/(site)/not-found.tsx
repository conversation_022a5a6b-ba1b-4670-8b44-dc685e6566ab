import { CMSLink } from "@/components/cms-link";
import type { Page } from "@/payload-types";
import React from "react";

export default function NotFound() {
	return (
		<main className="flex flex-col h-[100svh]">
			<div className="min-h-[50vh] bg-contrast w-full" />
			<div className="layout-block bg-primary flex items-center h-full">
				<div className="default-grid w-full">
					<div className="col-span-2 text-sm">404 Not Found</div>
					<h3 className="col-span-full col-start-3">
						Hoit aus. Des gibts jetzt ned.
						<br />
						<span className="ml-[calc(var(--width-columns-2)+var(--gap))]">
							Homma uns verrent?
						</span>
					</h3>
					<div className="col-start-5 mt-32">
						<CMSLink
							type="reference"
							appearance="default"
							reference={{
								relationTo: "pages",
								value: { slug: "home" } as Page,
							}}
						>
							Go home
						</CMSLink>
					</div>
				</div>
			</div>
		</main>
	);
}
