import { withPayload } from "@payloadcms/next/withPayload";

import redirects from "./redirects.js";

/** @type {import('next').NextConfig} */
const nextConfig = {
	images: {
		remotePatterns: [
			...["http://localhost:3000", "https://new.hard-chor.at"].map((item) => {
				if (!item) return undefined;

				const url = new URL(item);

				return {
					hostname: url.hostname,
					protocol: url.protocol.replace(":", ""),
				};
			}),
		],
	},
	webpack: (webpackConfig) => {
		webpackConfig.resolve.extensionAlias = {
			".cjs": [".cts", ".cjs"],
			".js": [".ts", ".tsx", ".js", ".jsx"],
			".mjs": [".mts", ".mjs"],
		};

		return webpackConfig;
	},
	reactStrictMode: true,
	redirects,
};

export default withPayload(nextConfig, { devBundleServerPackages: false });
